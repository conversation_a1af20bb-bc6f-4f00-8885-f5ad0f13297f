/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    stm32g4xx_it.c
  * @brief   Interrupt Service Routines.
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) 2021 STMicroelectronics.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by ST under Ultimate Liberty license
  * SLA0044, the "License"; You may not use this file except in compliance with
  * the License. You may obtain a copy of the License at:
  *                             www.st.com/SLA0044
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "uapp.h"
#include "stm32g4xx_it.h"
#include "stm32g4xx_ll_tim.h"
#include "uapp.h"
/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN TD */

/* USER CODE END TD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/* External variables --------------------------------------------------------*/

extern DAC_HandleTypeDef hdac1;

/* USER CODE BEGIN EV */

/* USER CODE END EV */

/******************************************************************************/
/*           Cortex-M4 Processor Interruption and Exception Handlers          */
/******************************************************************************/

/******************************************************************************/
/* stm32g4xx Peripheral Interrupt Handlers                                    */
/* Add here the Interrupt Handlers for the used peripherals.                  */
/* For the available peripheral interrupt handler names,                      */
/* please refer to the startup file (startup_stm32g4xx.s).                    */
/******************************************************************************/
extern DAC_HandleTypeDef hdac1;
/**
  * @brief This function handles TIM6 global interrupt, DAC1 and DAC3 channel underrun error interrupts.
  */
void TIM6_DAC_IRQHandler(void)
{
  /* USER CODE BEGIN TIM6_DAC_IRQn 0 */

  /* USER CODE END TIM6_DAC_IRQn 0 */
  HAL_DAC_IRQHandler(&hdac1);
  /* USER CODE BEGIN TIM6_DAC_IRQn 1 */

  /* USER CODE END TIM6_DAC_IRQn 1 */
}

extern DMA_HandleTypeDef hdma_usart3_tx;
void DMA1_Channel4_IRQHandler(void)
{
  /* USER CODE BEGIN DMA1_Channel5_IRQn 0 */
  
  /* USER CODE END DMA1_Channel5_IRQn 0 */
  HAL_DMA_IRQHandler(&hdma_usart3_tx);
  /* USER CODE BEGIN DMA1_Channel5_IRQn 1 */
    
  /* USER CODE END DMA1_Channel5_IRQn 1 */
}

uint32_t pvd_it_cnt = 0;
void HAL_PWR_PVDCallback(void)
{
    pvd_it_cnt++;
    rt_kprintf("\r\n mcu power off. ");
    /* almost 122us */
}

void ADC3_IRQHandler(void)
{
  /* Check whether ADC analog watchdog 1 caused the ADC interruption */
  if(LL_ADC_IsActiveFlag_AWD1(ADC3) != 0)
  {
    /* Clear flag ADC analog watchdog 1 */
    LL_ADC_ClearFlag_AWD1(ADC3);
    /* Disable ADC analog watchdog 1 interruption */
    LL_ADC_DisableIT_AWD1(ADC3);
    rt_kprintf(".A.");
    if(rt_tick_get() >= (RT_TICK_PER_SECOND*4))
    {
        /* Call interruption treatment function */
        rt_kprintf("AdcAnalogWatchdog1_Callback\r\n");
    }
  }
}

#include "stm32g4xx_ll_tim.h"
__IO uint32_t tim2_irq_cnt = 0;
extern void acin_transform2rms(void);
void TIM2_IRQHandler(void)
{
    if (LL_TIM_IsActiveFlag_UPDATE(TIM2))
    {
        LL_TIM_ClearFlag_UPDATE(TIM2);

        acin_transform2rms();

        tim2_irq_cnt++;
    }
}

__IO uint32_t tim20_irq_cnt = 0;

void TIM3_IRQHandler(void)
{
    if (LL_TIM_IsActiveFlag_UPDATE(TIM3))
    {
        LL_TIM_ClearFlag_UPDATE(TIM3);

        tim20_irq_cnt++;
    }
}



/**
  * @brief  This function handles the PVD Output interrupt request.
  * @param  None
  * @retval None
  */
void PVD_PVM_IRQHandler(void)
{
  HAL_PWREx_PVD_PVM_IRQHandler();
}
/* This section is present only when serial communication is used */
/**
  * @brief  This function handles USART interrupt request.
  * @param  None
  * @retval None
  */
void USARTx_IRQHandler(void)
{

}


void TIM1_CC_IRQHandler(void)
{
    /* enter interrupt */
	rt_interrupt_enter();

    if(LL_TIM_IsActiveFlag_CC1(TIM1) != RESET)
	{
        LL_TIM_ClearFlag_CC1(TIM1);
    }
	/* leave interrupt */
	rt_interrupt_leave();
    
    
}

void bsp_llc_pwm_disable(void);
void TIMx_Breakin_StopAllPWM(void)
{
    #ifdef VFD_TEST_DEBUG
    
    if((vfd.manual.inv_u_duty || vfd.manual.inv_v_duty || vfd.manual.inv_w_duty)
    || (vfd.manual.pfc_u_duty || vfd.manual.pfc_v_duty || vfd.manual.pfc_w_duty)
    || (vfd.manual.dcdc_u_duty || vfd.manual.dcdc_v_duty || vfd.manual.dcdc_w_duty)
    )
        return;
    #endif
    
    pfc.ControlState = ST_STOP;

    PfcDisPwm();
    
    vfd.stop_all_pwm_flag = 1;
}


extern TIM_HandleTypeDef htim1;
void TIM1_BRK_TIM15_IRQHandler(void)
{
  /* USER CODE BEGIN TIMx_BRK_M1_IRQn 0 */
  LL_TIM_DisableIT_BRK(TIM1);
    
  /* USER CODE END TIMx_BRK_M1_IRQn 0 */
  if (LL_TIM_IsActiveFlag_BRK(TIM1))
  {
     LL_TIM_ClearFlag_BRK(TIM1);
     
     hardware_irq_pin.f_tim1_breakin_flag = RT_TRUE;
     dio_table[dio_pin_find(F_IPM_INV_PIN)].irq_delay = IRQ_DELAY_DEFAULT;
     
     if( FHD_ERROR)
     {
        TIMx_Breakin_StopAllPWM();
     }
  }
  
  if (LL_TIM_IsActiveFlag_BRK2(TIM1))
  {
     LL_TIM_ClearFlag_BRK2(TIM1);
     hardware_irq_pin.f_tim1_breakin_flag = RT_TRUE;  
     dio_table[dio_pin_find(F_IPM_INV_PIN)].irq_delay = IRQ_DELAY_DEFAULT;
      
     if( FHD_ERROR)
     {
        TIMx_Breakin_StopAllPWM();
     }
  }
  
  /* USER CODE BEGIN TIMx_BRK_M1_IRQn 1 */

  /* USER CODE END TIMx_BRK_M1_IRQn 1 */
}



/* USER CODE BEGIN 1 */

/* USER CODE END 1 */
/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/

/******************** (C) COPYRIGHT 2021     ***********************************
* File Name          : app_sub.c
* Author             : xiou
* Version            : V1.0
* Date               :
* Description        : this is vfd system sub logic
                        * achieve at least io/led/ad/com ...

********************************************************************************/

#include <rtthread.h>
#include <rtdevice.h>
#include <board.h>
#include "uapp.h"
#include "time.h"
/* Private variables ------------------------------------------------------------*/

/* Private variables end---------------------------------------------------------*/

/* Private function prototypes --------------------------------------------------*/

/* Private function prototypes end-----------------------------------------------*/

int vfd_sub_init(void)
{
    com_cnters_init();//
    
    return 0;
}

void update_local_time_data(void)
{
    time_t cur_time;
    struct tm *cur_tm;
    
    cur_time = time(NULL);
    
    cur_tm = localtime(&cur_time);
    
    vfd.date.year    = ((1900+cur_tm->tm_year) < 2000) ? 0 : (1900+cur_tm->tm_year - 2000);
    vfd.date.month   =  cur_tm->tm_mon+1;
    vfd.date.day     =  cur_tm->tm_mday;
    vfd.date.hour    =  cur_tm->tm_hour;
    vfd.date.min     =  cur_tm->tm_min;
    vfd.date.sec     =  cur_tm->tm_sec;
    
}


int vfd_logdata_update(uint8_t *buff)
{
    	log_data_t *log = ( log_data_t * )buff;

	if( buff == RT_NULL )
		return -1;

	uint32_t data = 0;
    // offset + 23
    toolbox_u32_set_data( &buff[0],1,vfd.serial_addr );                     //设备id
	toolbox_u32_set_data( &buff[1],4,rt_tick_get()/RT_TICK_PER_SECOND );    //当前上电时间
    toolbox_u32_set_data( &buff[5],2,VFD_SOFT_VERSION );                    //软件版本号
	toolbox_u32_set_data( &buff[7],2,vfd.ctrl_hw_ver );                     //硬件版本号
	toolbox_u32_set_data( &buff[9],1,vfd.board_id );                        //型号
            
    rt_memcpy(&buff[10], (uint8_t *)&vfd.io,   4);   

//    U32_SET_BIT(buff[13], 1, (!DIO_NAME_READ_BIT("F_IPM_INV") || (DIO_NAME_READ_DELAY("F_IPM_INV") > 0)));
//    U32_SET_BIT(buff[13], 2, (!DIO_NAME_READ_BIT("F_IPM_PFC") || (DIO_NAME_READ_DELAY("F_IPM_PFC") > 0)));    
//    U32_SET_BIT(buff[12], 2, (!DIO_NAME_READ_BIT("I_HD_OC") || (DIO_NAME_READ_DELAY("I_HD_OC") > 0)));
//    U32_SET_BIT(buff[12], 3, (!DIO_NAME_READ_BIT("O_HD_OC") || (DIO_NAME_READ_DELAY("O_HD_OC") > 0)));
//    U32_SET_BIT(buff[13], 0, (!DIO_NAME_READ_BIT("OVP_P_BUS") || (DIO_NAME_READ_DELAY("OVP_P_BUS") > 0)));
//    U32_SET_BIT(buff[11], 5, (!DIO_NAME_READ_BIT("F_HD") || (DIO_NAME_READ_DELAY("F_HD") > 0)));
//    U32_SET_BIT(buff[11], 4, !DIO_NAME_READ_BIT("CLR_HD"));
//    U32_SET_BIT(buff[11], 6, DIO_NAME_READ_BIT("FS_DR"));
//    U32_SET_BIT(buff[11], 7, !DIO_NAME_READ_BIT("FS_DR_B")); 
//    U32_SET_BIT(buff[10], 7, (!DIO_NAME_READ_BIT("TPSW") || (DIO_NAME_READ_DELAY("TPSW") > 0)));        
//    U32_SET_BIT(buff[12], 4, (!DIO_NAME_READ_BIT("PFC_CBC") || (DIO_NAME_READ_DELAY("PFC_CBC") > 0))); 
//    U32_SET_BIT(buff[12], 6, (!DIO_NAME_READ_BIT("INV_CBC") || (DIO_NAME_READ_DELAY("INV_CBC") > 0)));
//    U32_SET_BIT(buff[11], 0, (!DIO_NAME_READ_BIT("KMON_FLT") || (DIO_NAME_READ_DELAY("KMON_FLT") > 0)));
      
    rt_memcpy(&buff[16], (uint8_t *)&vfd.bit,  5);
    rt_memcpy(&buff[22], (uint8_t *)&vfd.diag, 8);
    //rt_memcpy(&buff[30], (uint8_t *)&vfd.ctrl, 18);
    toolbox_u32_set_data(&buff[30], 1, vfd.ctrl.sys_st);        //系统状态
    toolbox_u32_set_data(&buff[31], 1, vfd.ctrl.inv_st);        //输出状态
    toolbox_u32_set_data(&buff[32], 1, mcsdk.ControlState);     //电驱状态
    toolbox_u32_set_data(&buff[34], 2, vfd.ctrl.motor_speed);   //电机速度
    toolbox_u32_set_data(&buff[36], 2, vfd.ctrl.set_speed);     //目标速度
    toolbox_u32_set_data(&buff[38], 2, vfd.ctrl.freq_out);      //输出频率
    toolbox_u32_set_data(&buff[40], 2, vfd.ctrl.set_freq);      //目标频率
        
    toolbox_u32_set_data(&buff[48], 2, (int16_t)vfd.filter_ad.ac_vin_r);
    toolbox_u32_set_data(&buff[50], 2, (int16_t)vfd.filter_ad.ac_vin_s);
    toolbox_u32_set_data(&buff[52], 2, (int16_t)vfd.filter_ad.ac_vin_t);
    toolbox_u32_set_data(&buff[54], 2, (int16_t)(vfd.filter_ad.ac_iin_r* 10));
    toolbox_u32_set_data(&buff[56], 2, (int16_t)(vfd.filter_ad.ac_iin_s* 10));
    toolbox_u32_set_data(&buff[58], 2, (int16_t)(vfd.filter_ad.ac_iin_t* 10));
    toolbox_u32_set_data(&buff[60], 2, (int16_t)(vfd.acin_freq * 10));
    
    toolbox_u32_set_data(&buff[62], 2, (int16_t)vfd.filter_ad.dcVin);                   //dcvin DC输入电压
    toolbox_u32_set_data(&buff[64], 1, vfd.voltInputType);                              //供电方式
    toolbox_u32_set_data(&buff[65], 1, vfd.driveMotorType);                             //驱动方式
    toolbox_u32_set_data(&buff[66], 2, (int16_t)(vfd.startDC_Cur * 10));                //支流驱动电流
    toolbox_u32_set_data(&buff[68], 2, vfd.startDC_SecTick);                            //直流驱动时间
    toolbox_u32_set_data(&buff[70], 2, (int16_t)(vfd.filter_ad.vbus_inv * 10));         //Vbus 母线电压
   
    toolbox_u32_set_data(&buff[72], 2, (int16_t)vfd.filter_ad.ac_vout_u);           //
    toolbox_u32_set_data(&buff[74], 2, (int16_t)vfd.filter_ad.ac_vout_v);           //
    toolbox_u32_set_data(&buff[76], 2, (int16_t)vfd.filter_ad.ac_vout_w);           //
    toolbox_u32_set_data(&buff[78], 2, (int16_t)(vfd.filter_ad.ac_iout_u * 10));
    toolbox_u32_set_data(&buff[80], 2, (int16_t)(vfd.filter_ad.ac_iout_v * 10));
    toolbox_u32_set_data(&buff[82], 2, (int16_t)(vfd.filter_ad.ac_iout_w * 10));
    toolbox_u32_set_data(&buff[84], 2, (int16_t)vfd.filter_ad.ac_vout);             //输出电压
    toolbox_u32_set_data(&buff[86], 2, motor_get_FcApply());                        //载波频率
    
    toolbox_u32_set_data(&buff[88], 2, vfd.invc_err_code);                          //电驱故障码 
    
    toolbox_u32_set_data(&buff[90], 2, (int16_t)(0));                                   //3.3V
    toolbox_u32_set_data(&buff[92], 2, (int16_t)(vfd.filter_ad.dc_5V * 10));            //5V
    toolbox_u32_set_data(&buff[94], 2, (int16_t)(vfd.filter_ad.dc_12V * 10));           //15V
    toolbox_u32_set_data(&buff[96], 2, (int16_t)(vfd.filter_ad.dc_24V * 10));           //24V
    toolbox_u32_set_data(&buff[98], 2, (int16_t)(vfd.filter_ad.aux1 * 10));           
    toolbox_u32_set_data(&buff[100], 2, (int16_t)(vfd.filter_ad.aux2 * 10)); 
    
    toolbox_u32_set_data(&buff[102], 1, 0);                                          //MCUtemp
    toolbox_u32_set_data(&buff[103], 1, (int16_t)vfd.filter_ad.temp_BUS_CAP);
    toolbox_u32_set_data(&buff[104], 1, (int16_t)vfd.filter_ad.temp_BUS_CAP2);
    toolbox_u32_set_data(&buff[105], 1, (int16_t)vfd.filter_ad.temp_EMC_L);
    toolbox_u32_set_data(&buff[106], 1, (int16_t)vfd.filter_ad.temp_PFC_L);
    toolbox_u32_set_data(&buff[107], 1, (int16_t)vfd.filter_ad.temp_pfc_mos);        //pfc模块温度
    toolbox_u32_set_data(&buff[108], 1, (int16_t)vfd.filter_ad.temp_POW_DIODE); 
    toolbox_u32_set_data(&buff[109], 1, (int16_t)vfd.filter_ad.temp_hdc1080);        
    toolbox_u32_set_data(&buff[110], 1, (int16_t)vfd.filter_ad.mosi_hdc1080);
}

int vfd_logrtc_update(uint8_t *buff)
{
    log_head_t *log = (log_head_t *)buff;
    
    if(buff == RT_NULL)
        return -1;
    
    update_local_time_data();
    
    log->date.year  = vfd.date.year;
    log->date.month = vfd.date.month;
    log->date.day   = vfd.date.day;
    log->date.hour  = vfd.date.hour;
    log->date.min   = vfd.date.min;
    log->date.sec   = vfd.date.sec;
    
}


void vfd_fs_set(uint8_t fs_chnn,uint8_t is_enable)
{
    static uint8_t test_cmd = 0;
    static uint8_t prev_enable = 0xFF;
    uint8_t pin_is_enable  = 0;
    
    switch(fs_chnn)
    {
        case FS_CHNN1:
            vfd.ctrl.fs_boost = is_enable;break;
        case FS_CHNN2:
            vfd.ctrl.fs_pfc = is_enable;break;
        case FS_CHNN3:
            vfd.ctrl.fs_inv = is_enable;break;
        case FS_CHNN4:
            test_cmd = is_enable;break;
            
    }
    
    pin_is_enable = vfd.ctrl.fs_boost || vfd.ctrl.fs_pfc || vfd.ctrl.fs_inv || test_cmd;
    
    if(prev_enable != pin_is_enable)
    {
        if(pin_is_enable)
            rt_pin_write(FS_DR_PIN,PIN_HIGH);
        else
            rt_pin_write(FS_DR_PIN,PIN_LOW);
    }
    
    prev_enable = pin_is_enable;
}

void com_cnter_deinit(com_t *ds,uint32_t time_out_set)
{
    rt_memset(ds,0,sizeof(com_t));
    ds->flag_normal = 0;
    ds->flag_timeout = 0;
    ds->timeout_cnt = 0;
    ds->timeout_set = time_out_set;
}

void com_cnters_init(void)
{
    com_cnter_deinit(&com_485,1000);
    com_cnter_deinit(&com_can,1000);
    com_cnter_deinit(&com_ptu,6000);
    com_cnter_deinit(&com_uart3_pfc,500);
    com_cnter_deinit(&com_modbus,1000);
    com_cnter_deinit(&com_wifi,500);
}

void com_cnter_recv(com_t *ds)
{
    uint32_t now_tick = rt_tick_get()*1000/RT_TICK_PER_SECOND;
    ds->rx_cnt++;
    ds->timeout_cnt = 0;
    ds->flag_normal  = 1;
    ds->flag_timeout = 0;
    ds->since_last_rx_tick = now_tick - ds->last_rx_tick;
    ds->last_rx_tick = now_tick;
    ds->flag_led = 1;
}    

void com_cnter_send(com_t *ds)
{
    uint32_t now_tick = rt_tick_get();
    ds->tx_cnt++;
    ds->flag_led = 0;
}

void com_cnter_timeout_ticking(com_t *ds)
{
    ds->timeout_cnt++;
    
    if(ds->timeout_set && 
        (ds->timeout_cnt >= ds->timeout_set))
    {
        ds->flag_normal  = 0;
        ds->flag_timeout = 1;
        ds->flag_led = 0;
    }
}

extern uint8_t rs485_init;
void com_cnters_ticking(void)
{
    static uint16_t cnt = 0;
    static uint8_t com_ptu_flag = 0;
    static uint8_t com_modbus_flag = 0;
    
    com_cnter_timeout_ticking(&com_485);
    com_cnter_timeout_ticking(&com_can);
    com_cnter_timeout_ticking(&com_ptu);
    com_cnter_timeout_ticking(&com_uart3_pfc);
    com_cnter_timeout_ticking(&com_modbus);
    com_cnter_timeout_ticking(&com_wifi);
    
    if(com_ptu_flag != com_ptu.flag_normal)
    {
        com_ptu_flag = com_ptu.flag_normal;
        if(com_ptu_flag)
            record_logdata_push(LOG_PtuOn,0);
        else
            record_logdata_push(LOG_PtuOff,0);
    }
    
    if(com_modbus_flag != com_modbus.flag_normal)
    {
        com_modbus_flag = com_modbus.flag_normal;
        if(com_modbus_flag)
            record_logdata_push(LOG_ModbusOn,0);
        else
            record_logdata_push(LOG_ModbusOff,0);
    }
    
    if(rs485_init)
    {
        //rs485 de 0->tx 1->rx
        if(HAL_GPIO_ReadPin(GPIOA,GPIO_PIN_1) == 0)
        {
            if(cnt <= 100)
                cnt++;
            else
            {
                rt_kprintf(" time out to set rs485 de to rx \r\n");
                void rs485_toggle_de(void);
                cnt = 0;
                rs485_toggle_de();
            }
        }
        else
            cnt = 0;
    }
}

int vfd_get_err_stop(void)
{
    int result;
    
    return result;
}

int led_control_logic(void)
{
    static uint8_t check_cnt = 0;
    static uint32_t _tick_cnt = 0;
    static uint8_t  _run_out = 0;
    static uint8_t  _flt_out = 0;
    static uint8_t  _485_out = 0;
    static uint8_t  _can_out = 0;
    _tick_cnt++;
    
    if(check_cnt < 7)
    {
        if(_tick_cnt % 50 == 0)
        {
            _run_out = 1 - _run_out;
            rt_pin_write(LED_RUN_PIN,_run_out);
            rt_pin_write(LED_FLT_PIN,_run_out);
            rt_pin_write(LED_CAN_PIN,_run_out);
            rt_pin_write(LED_485_PIN,_run_out);
            check_cnt++;
        }
        return 0;
    }
    
    // run led 1hz
    if(((vfd.inverter->ControlState == ST_START) || (vfd.inverter->ControlState == ST_RUN))
        &&
            (((vfd.pfc->ControlState == ST_START) || (vfd.pfc->ControlState == ST_RUN)))
    )
    {
        if((_tick_cnt % 4) == 0)
        {    _run_out = 1 - _run_out;
            rt_pin_write(LED_RUN_PIN,_run_out);
        }
    }
    else if(((vfd.inverter->ControlState == ST_START) || (vfd.inverter->ControlState == ST_RUN))
    )
    {
        if((_tick_cnt % 10) == 0)
        {    _run_out = 1 - _run_out;
            rt_pin_write(LED_RUN_PIN,_run_out);
        }
    }
    else if(((vfd.pfc->ControlState == ST_START) || (vfd.pfc->ControlState == ST_RUN))
    )
    {
        if((_tick_cnt % 16) == 0)
        {    _run_out = 1 - _run_out;
            rt_pin_write(LED_RUN_PIN,_run_out);
        }
    }
    else if(_tick_cnt % 50 == 0)
    {
        _run_out = 1 - _run_out;
		rt_pin_write(LED_RUN_PIN,_run_out);
    }
    
    
    // flt led keep on when hardware occured
    if( vfd.bit.diag_hard             ||  vfd.bit.lock_stop                  || 
//       ((!DIO_READ_BIT(F_IPM_DC_PIN)   || (DIO_IRQ_DELAY(F_IPM_DC_PIN) > 0))  ||
//       (!DIO_READ_BIT(F_IPM_PFC_PIN)  || (DIO_IRQ_DELAY(F_IPM_PFC_PIN) > 0)) || 
//       (!DIO_READ_BIT(F_IPM_INV_PIN)  || (DIO_IRQ_DELAY(F_IPM_INV_PIN) > 0)) || 
//       (!DIO_READ_BIT(OVP_P_BUS_PIN)  || (DIO_IRQ_DELAY(OVP_P_BUS_PIN) > 0)) ||
//       (!DIO_READ_BIT(OCP_N_PIN)      || (DIO_IRQ_DELAY(OCP_N_PIN) > 0))     ||
//       (!DIO_READ_BIT(OCP_P_PIN)      || (DIO_IRQ_DELAY(OCP_P_PIN) > 0))     ||
//       (!DIO_READ_BIT(ICP_N_PIN)      || (DIO_IRQ_DELAY(ICP_N_PIN) > 0))     ||
//       (!DIO_READ_BIT(ICP_P_PIN)      || (DIO_IRQ_DELAY(ICP_P_PIN) > 0))     ||
       (!DIO_READ_BIT(F_HD_PIN)       || (DIO_IRQ_DELAY(F_HD_PIN) > 0))
    )
    {
        rt_pin_write(LED_FLT_PIN,LED_ON );
    }
    // flt led 2hz when software occured
    else if( vfd.bit.diag_soft )
    {
        if(_tick_cnt % 50 == 0)
        {
            _flt_out = 1 - _flt_out;
            rt_pin_write(LED_FLT_PIN,_flt_out );
        }
    }
    // flt led 2hz when software occured
    else if( vfd.bit.diag_pfc_soft ||vfd.bit.diag_pfc_hard )
    {
        if(_tick_cnt % 25 == 0)
        {
            _flt_out = 1 - _flt_out;
            rt_pin_write(LED_FLT_PIN,_flt_out );
        }
    }
    // flt led keep off
    else
    {
        rt_pin_write(LED_FLT_PIN,LED_OFF );
    }
    
    // 485 led on/off with data rx/tx
    if(com_485.timeout_cnt <= 150)
    {
        if(_tick_cnt % 30 == 0)
        {
            _485_out = 1 - _485_out;
            rt_pin_write(LED_485_PIN, _485_out);
        }
    }
    else
    {
        rt_pin_write(LED_485_PIN,  LED_OFF );
    }
    
    // can led on/off with data rx/tx
    if(com_can.timeout_cnt <= 150)
    {
        if(_tick_cnt % 30 == 0)
        {
            _can_out = 1 - _can_out;
            rt_pin_write(LED_CAN_PIN,_can_out);
        }
    }
    else
    {
        rt_pin_write(LED_CAN_PIN,LED_OFF );
    }
    
    return 0;
}

void WriteSetInfo(void)
{
    if (vfd.nvs_write_config_flag == 1)
    {
		rt_memcpy(&nvs_datas.config.SetAdCa,    &vfd.SetAdCa,    sizeof(vfd.SetAdCa));
		rt_memcpy(&nvs_datas.config.SetHwVer,   &vfd.SetHwVer,   sizeof(vfd.SetHwVer));
		rt_memcpy(&nvs_datas.config.SetCanAddr, &vfd.SetCanAddr, sizeof(vfd.SetCanAddr));

		//-------------------------------------------------------------
		// ���õĲ���д��Flash�洢
		//-------------------------------------------------------------
		nvsdata_write_config();
		vfd.nvs_write_config_flag = 0;
    }
}

void ReadSetInfo(void)
{
	//-------------------------------------------------------------
	// ��Flash���ز������ڴ�
	//-------------------------------------------------------------
	nvsdata_read_config();
	rt_memcpy(&vfd.SetAdCa,    &nvs_datas.config.SetAdCa,    sizeof(vfd.SetAdCa));
	rt_memcpy(&vfd.SetHwVer,   &nvs_datas.config.SetHwVer,   sizeof(vfd.SetHwVer));
	rt_memcpy(&vfd.SetCanAddr, &nvs_datas.config.SetCanAddr, sizeof(vfd.SetCanAddr));	
		
}

extern __IO uint32_t tim2_irq_cnt;
extern int rt_wdt_start(void);
void nvsdata_write_task(void)
{
    static uint8_t start_wdg_flag = 0;
    int enter_tick,leave_tick;
    static uint8_t prev_st = 0;
    static uint32_t sec = 0;
    static uint16_t config_crc = 0;
    
    if((prev_st != vfd.ctrl.inv_st) &&
            (prev_st == ST_RUN))
    {
        nvsdata_write_acc();
    }
    
    prev_st = vfd.ctrl.inv_st;
    
    if(sec != (rt_tick_get()/RT_TICK_PER_SECOND))
    {
        sec = (rt_tick_get()/RT_TICK_PER_SECOND);
            
        if((sec >= 10) && (start_wdg_flag == 0))
        {
            #if (BOARD_USE_WATCH_DOG)
            rt_wdt_start();
            rt_wdt_set_timeout(2);
            #endif
            start_wdg_flag = 1;
        }
        
        if(sec >= 10)
        {
            static uint8_t div = 10;
            
            if(sec % div == 0)
            {
                nvsdata_write_acc();
            }
            
            if(vfd.ctrl.sys_st == ST_RUN)
                div = 30; // write every 30s
            else 
                div = 60;// write every 60s
            
            
        }
    }  
}

static uint8_t fan_start_flag = 0;

int vfd_start_fan(uint16_t duty, uint16_t freq)
{ 
    if(fan_start_flag == 0)
    {
        fan_start_flag = 1;
        __HAL_TIM_ENABLE(&htim5);
        TIM_CCxChannelCmd(htim5.Instance, TIM_CHANNEL_2, TIM_CCx_ENABLE);
    }
    
    duty = (duty > 100) ? 100 : duty;
    
    if(vfd.fan_duty != duty)
    {
		vfd.fan_duty = duty; // ��¼����
        
        uint16_t cnt = (htim5.Init.Period * duty / 100) > 0 ? (htim5.Init.Period * duty / 100 - 1) : 0;
        __HAL_TIM_SET_COMPARE(&htim5, TIM_CHANNEL_2, cnt);   
    }
}

int vfd_stop_fan(void)
{
    fan_start_flag = 0;
	vfd.fan_duty = 0;//��¼����
    __HAL_TIM_DISABLE(&htim5);
    __HAL_TIM_SET_COUNTER(&htim5, 0);
    TIM_CCxChannelCmd(htim5.Instance, TIM_CHANNEL_2, TIM_CCx_DISABLE);
}

void vfd_cooling_fan_logic(void)
{
    static uint32_t fan_stop_delay = 0;
    
    if(vfd.ctrl.inv_st == ST_RUN)
    {
        vfd_start_fan(100,NULL);
        fan_stop_delay = 0;
    }
    else
    {
        if(timer_relay_ticking(1,&fan_stop_delay,60*100) == true) /* 1= 10ms */
        {
            vfd_stop_fan();
        }
    }
}


void ParGet10MsFromNvs(void)
{


}

void funcCodeSetDefault(void)
{

}